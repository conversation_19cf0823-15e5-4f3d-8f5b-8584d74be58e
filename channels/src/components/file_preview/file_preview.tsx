// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import type {ReactNode} from 'react';

import type {FileInfo} from '@mattermost/types/files';

import {getFileThumbnailUrl, getFileUrl} from 'mattermost-redux/utils/file_utils';

import MediaEditor from 'components/custom_status/media_editor';
import FilenameOverlay from 'components/file_attachment/filename_overlay';
import FilePreviewModal from 'components/file_preview_modal';
import RootPortal from 'components/root_portal';

import Constants, {FileTypes, ModalIdentifiers} from 'utils/constants';
import * as Utils from 'utils/utils';

import type {ModalData} from 'types/actions';

import FileProgressPreview from './file_progress_preview';

type UploadInfo = {
    name: string;
    percent?: number;
    type?: string;
}
export type FilePreviewInfo = FileInfo & UploadInfo;

type Props = {
    enableSVGs: boolean;
    onRemove?: (id: string) => void;
    fileInfos: FilePreviewInfo[];
    uploadsInProgress?: string[];
    uploadsProgressPercent?: {[clientID: string]: FilePreviewInfo};
    actions?: {
        openModal: <P>(modalData: ModalData<P>) => void;
    };
}

export default class FilePreview extends React.PureComponent<Props> {
    static defaultProps = {
        fileInfos: [],
        uploadsInProgress: [],
        uploadsProgressPercent: {},
    };

    state = {
        isMediaEditMode: false,
        selectedFile: null as File | null,
    };

    imageEditorRef = React.createRef<any>();

    handleRemove = (id: string, e?: React.MouseEvent) => {
        e?.stopPropagation();
        this.props.onRemove?.(id);
    };

    handleShowImagePreview = async (fileInfo: FileInfo) => {
        try {
            const fileUrl = getFileUrl(fileInfo.id);
            const response = await fetch(fileUrl);
            const blob = await response.blob();

            let finalFile: File;

            if (fileInfo.extension === 'svg') {
                // تحويل SVG إلى PNG للتحرير
                finalFile = await this.convertSvgToPng(blob, fileInfo.name);
            } else {
                // تحديد نوع الملف بناءً على الامتداد
                let mimeType = fileInfo.mime_type || 'application/octet-stream';
                if (fileInfo.extension === 'png') {
                    mimeType = 'image/png';
                } else if (fileInfo.extension === 'jpg' || fileInfo.extension === 'jpeg') {
                    mimeType = 'image/jpeg';
                } else if (fileInfo.extension === 'gif') {
                    mimeType = 'image/gif';
                }

                finalFile = new File([blob], fileInfo.name, {
                    type: mimeType,
                    lastModified: fileInfo.create_at,
                });
            }

            this.setState({
                selectedFile: finalFile,
                isMediaEditMode: true,
            });
        } catch (err) {
            // eslint-disable-next-line no-console
            console.error('فشل فتح MediaEditor:', err);
        }
    };

    handleShowModalPreview = (fileInfo: FileInfo) => {
        const {actions} = this.props;

        const notAllowedExtensions = ['pdf', 'docx', 'doc', 'dotx', 'xlsx', 'xls', 'xlsm', 'pptx', 'ppt', 'ppsx'];
        if (!notAllowedExtensions.includes(fileInfo.extension)) {
            actions?.openModal?.({
                modalId: ModalIdentifiers.FILE_PREVIEW_MODAL,
                dialogType: FilePreviewModal,
                dialogProps: {
                    fileInfos: [fileInfo],
                    postId: fileInfo.post_id,
                    startIndex: 0,
                },
            });
        }
    };

    convertSvgToPng = (svgBlob: Blob, fileName: string): Promise<File> => {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            img.onload = () => {
                canvas.width = img.width || 800;
                canvas.height = img.height || 600;

                if (ctx) {
                    ctx.drawImage(img, 0, 0);
                    canvas.toBlob((blob) => {
                        if (blob) {
                            const pngFile = new File([blob], fileName.replace('.svg', '.png'), {
                                type: 'image/png',
                                lastModified: Date.now(),
                            });
                            resolve(pngFile);
                        } else {
                            reject(new Error('فشل في تحويل SVG إلى PNG'));
                        }
                    }, 'image/png');
                } else {
                    reject(new Error('فشل في إنشاء canvas context'));
                }
            };

            img.onerror = () => reject(new Error('فشل في تحميل SVG'));
            img.src = URL.createObjectURL(svgBlob);
        });
    };

    handleMediaEditorSave = () => {
        this.setState({isMediaEditMode: false, selectedFile: null});
    };

    handleMediaEditorClose = () => {
        this.setState({isMediaEditMode: false, selectedFile: null});
    };

    render() {
        const previews: ReactNode[] = [];

        this.props.fileInfos.forEach((info) => {
            const type = Utils.getFileType(info.extension);

            let className = 'file-preview post-image__column';
            let previewImage;

            if (type === FileTypes.SVG && this.props.enableSVGs) {
                previewImage = (
                    <img
                        alt='file preview'
                        className='post-image normal'
                        src={getFileUrl(info.id)}
                    />
                );
            } else if (type === FileTypes.IMAGE) {
                let imageClassName = 'post-image';

                if ((info.width && info.width < Constants.THUMBNAIL_WIDTH) && (info.height && info.height < Constants.THUMBNAIL_HEIGHT)) {
                    imageClassName += ' small';
                } else {
                    imageClassName += ' normal';
                }

                let thumbnailUrl = getFileThumbnailUrl(info.id);
                if (Utils.isGIFImage(info.extension) && !info.has_preview_image) {
                    thumbnailUrl = getFileUrl(info.id);
                }

                previewImage = (
                    <div
                        className={imageClassName}
                        style={{
                            backgroundImage: `url(${thumbnailUrl})`,
                            backgroundSize: 'cover',
                        }}
                    />
                );
            } else {
                className += ' custom-file';
                previewImage = <div className={'file-icon ' + Utils.getIconClassName(type)}/>;
            }

            previews.push(
                <div
                    key={info.id}
                    className={className}
                    onClick={() => {
                        if (type === FileTypes.IMAGE || (type === FileTypes.SVG && this.props.enableSVGs)) {
                            // الصور => MediaEditor
                            this.handleShowImagePreview(info);
                        } else {
                            // باقي الملفات => FilePreviewModal
                            this.handleShowModalPreview(info);
                        }
                    }}
                >
                    <div className='post-image__thumbnail'>
                        {previewImage}
                    </div>
                    <div className='post-image__details'>
                        <div className='post-image__detail_wrapper'>
                            <div className='post-image__detail'>
                                <FilenameOverlay
                                    fileInfo={info}
                                    compactDisplay={false}
                                    canDownload={false}
                                />
                                {info.extension && <span className='post-image__type'>{info.extension.toUpperCase()}</span>}
                                <span className='post-image__size'>{Utils.fileSizeToString(info.size)}</span>
                            </div>
                        </div>
                        <div>
                            {Boolean(this.props.onRemove) && (
                                <a
                                    className='file-preview__remove'
                                    onClick={this.handleRemove.bind(this, info.id)}
                                >
                                    <i className='icon icon-close'/>
                                </a>
                            )}
                        </div>
                    </div>
                </div>,
            );
        });

        if (this.props.uploadsInProgress && this.props.uploadsProgressPercent) {
            const uploadsProgressPercent = this.props.uploadsProgressPercent;
            this.props.uploadsInProgress.forEach((clientId) => {
                const fileInfo = uploadsProgressPercent[clientId];
                if (fileInfo) {
                    previews.push(
                        <FileProgressPreview
                            key={clientId}
                            clientId={clientId}
                            fileInfo={fileInfo}
                            handleRemove={this.handleRemove}
                        />,
                    );
                }
            });
        }

        return (
            <>
                <div className='file-preview__container'>
                    {previews}
                </div>

                {this.state.isMediaEditMode && this.state.selectedFile && (
                    <RootPortal>
                        <MediaEditor
                            ref={this.imageEditorRef}
                            file={this.state.selectedFile}
                            onSave={this.handleMediaEditorSave}
                            onCancel={this.handleMediaEditorClose}
                        />
                    </RootPortal>
                )}
            </>
        );
    }
}
