{"name": "@mattermost/webapp", "private": true, "engines": {"node": ">=18.10.0", "npm": "^9.0.0 || ^10.0.0"}, "scripts": {"postinstall": "patch-package && npm run build --workspace=platform/types --workspace=platform/client --workspace=platform/components", "build": "node scripts/build.mjs", "run": "node scripts/run.mjs", "dev-server": "node scripts/dev-server.mjs", "test": "npm run test --workspaces --if-present", "test:updatesnapshot": "npm run test:updatesnapshot --workspaces --if-present", "test-ci": "npm run test-ci --workspaces --if-present", "check": "npm run check --workspaces --if-present", "fix": "npm run fix --workspaces --if-present", "check-types": "npm run check-types --workspaces --if-present", "clean": "npm run clean --workspaces --if-present", "gen-lang-imports": "node scripts/gen_lang_imports.mjs"}, "dependencies": {"@ffmpeg/ffmpeg": "0.12.15", "@ffmpeg/util": "0.12.2", "@fortawesome/fontawesome-free": "6.6.0", "@fortawesome/fontawesome-svg-core": "7.0.0", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/react-fontawesome": "0.2.2", "@toast-ui/react-image-editor": "3.15.2", "core-js": "3.39.0", "exceljs": "4.4.0", "lucide-react": "0.471.1", "react": "17.0.2", "react-collapsible": "2.10.0", "react-contextmenu": "2.14.0", "react-dom": "17.0.2", "react-icons": "5.4.0", "react-input-autosize": "3.0.0", "react-intl": "6.6.2", "react-loading": "2.0.3", "react-player": "2.16.0", "typescript": "5.6.3"}, "devDependencies": {"@babel/core": "7.22.0", "@babel/preset-env": "7.21.5", "@babel/preset-react": "7.18.6", "@babel/preset-typescript": "7.21.5", "@types/node": "20.11.30", "@types/webpack-env": "1.18.5", "babel-loader": "9.1.2", "babel-plugin-formatjs": "10.5.1", "babel-plugin-styled-components": "2.1.1", "babel-plugin-typescript-to-proptypes": "2.1.0", "blessed": "0.1.81", "chalk": "5.3.0", "concurrently": "9.0.1", "cross-env": "7.0.3", "css-loader": "6.7.3", "eslint": "8.57.0", "eslint-import-resolver-webpack": "0.13.8", "eslint-plugin-formatjs": "4.12.2", "eslint-plugin-react": "7.34.0", "eslint-plugin-react-hooks": "4.6.0", "mini-css-extract-plugin": "2.7.5", "patch-package": "8.0.0", "sass": "1.80.5", "sass-loader": "16.0.2", "strip-ansi": "7.1.0", "style-loader": "4.0.0", "webpack": "5.95.0", "webpack-cli": "5.1.4", "webpack-dev-server": "5.1.0"}, "overrides": {"@floating-ui/react": {"react": "17.0.2", "react-dom": "17.0.2"}, "@giphy/react-components": {"styled-components": "5.3.6"}, "@mui/base": {"react": "17.0.2", "react-dom": "17.0.2"}, "enzyme-adapter-utils": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-bootstrap": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-color": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-contextmenu": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-custom-scrollbars": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-overlays": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-select": {"react": "17.0.2", "react-dom": "17.0.2"}, "react-transition-group": {"react": "17.0.2", "react-dom": "17.0.2"}, "braces": "3.0.3", "cheerio": "1.0.0-rc.12", "@types/estree": "0.0.51", "asn1.js": "5.4.1"}, "workspaces": ["channels", "platform/client", "platform/components", "platform/eslint-plugin", "platform/types"]}