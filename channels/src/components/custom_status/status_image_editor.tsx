// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import ImageEditor from '@toast-ui/react-image-editor';
import type {Ref} from 'react';
import React, {forwardRef, useEffect} from 'react';
import 'tui-image-editor/dist/tui-image-editor.css';
import {FormattedMessage} from 'react-intl';

type StatusImageEditorProps = {
    file: File;
    ref: Ref<HTMLDivElement>;
    onSave: (file?: File) => void;
    onCancel: () => void;
};

const StatusImageEditor = forwardRef<HTMLDivElement, StatusImageEditorProps>(({file, onSave, onCancel}, ref) => {
    const locale_ar = {
        Download: 'تحميل',
        Load: 'رفع',
        DeleteAll: 'حذف الكل',
        Delete: 'حذف',
        Reset: 'إعادة الضبط',
        Redo: 'تقدم',
        Undo: 'تراجع',
        History: 'السجل',
        Hand: 'اليد',
        ZoomOut: 'تصغير',
        ZoomIn: 'تكبير',
        Draw: 'رسم',
        Text: 'نص',
        Filter: 'فلتر',
        Shape: 'الأشكال',
        Crop: 'قص',
        Apply: 'تطبيق',
        Cancel: 'الغاء',
        Color: 'اللون',
        Straight: 'مستقيم',
        Free: 'حُر',
        Range: 'الحجم',
        Right: 'يمين',
        Center: 'المركز',
        Left: 'يسار',
        Underline: 'خط سُفلي',
        Italic: 'مائل',
        Bold: 'عريض',
        'Text size': 'حجم النص',
    };

    const theme = {
        'downloadButton.backgroundColor': 'var(--sofa-color)',
        'downloadButton.borderColor': '#046d5c',
        'common.fontFamily': 'GraphikArabic',
    };

    return (
        <div id='image-editor'>
            <div className='px-6 image-editor__header'>
                <h1 className='image-editor__header__title'>
                    <FormattedMessage
                        id='media_editor.image.header'
                        defaultMessage='Editing Image'
                    />
                </h1>
                <div>
                    <button
                        className='btn btn-secondary'
                        onClick={onCancel}
                    >
                        <FormattedMessage
                            id='media_editor.image.cancel_btn'
                            defaultMessage='Cancel'
                        />
                    </button>
                    <button
                        className='btn btn-primary'
                        onClick={() => onSave()}
                    >
                        <FormattedMessage
                            id='media_editor.image.save_btn'
                            defaultMessage='Save'
                        />
                    </button>
                </div>
            </div>
            <ImageEditor
                ref={ref}
                includeUI={{
                    loadImage: {
                        path: URL.createObjectURL(file),
                        name: 'SampleImage',
                    },
                    locale: locale_ar,
                    theme,
                    menu: ['crop', 'shape', 'text', 'draw'],
                    initMenu: 'draw',
                    uiSize: {
                        width: '100%',
                        height: '500px',
                    },
                    menuBarPosition: 'bottom',
                }}
                cssMaxHeight={500}
                cssMaxWidth={700}
                selectionStyle={{
                    cornerSize: 20,
                    rotatingPointOffset: 70,
                }}
                usageStatistics={true}
            />
        </div>
    );
});

export default StatusImageEditor;
