// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {useSelector} from 'react-redux';

import {getTheme} from 'mattermost-redux/selectors/entities/preferences';
import {makeStyleFromTheme} from 'mattermost-redux/utils/theme_utils';

import ExternalLink from 'components/external_link';

import svgs from './svgs';

interface Props {
    link: string;
}

// 🔹 تعريف getStyle (دالة تقبل theme كمعامل)
const getStyle = makeStyleFromTheme((theme) => {
    return {
        attachment: {
            marginLeft: '-20px',
            position: 'relative',
        },
        content: {
            borderTop: 'solid',
            borderWidth: '1px',
            borderColor: theme.centerChannelColor || '#BDBDBF',
            margin: '5px 0 5px 20px',
            padding: '2px 5px',
        },
        container: {
            padding: '10px',
        },
        body: {
            overflowX: 'auto',
            overflowY: 'hidden',
            paddingRight: '5px',
            width: '100%',
        },
        span: {
            display: 'none',
        },
        title: {
            fontSize: '16px',
            fontWeight: '600',
            height: '22px',
            lineHeight: '18px',
            margin: '5px 0 1px 0',
            padding: '0',
            color: theme.centerChannelColor || '#000',
        },
        title1: {
            textAlign: 'center',
            paddingTop: '5px',
            borderRadius: '11px',
            border: 'solid',
            borderWidth: '1px',
            borderColor: theme.centerChannelColor || '#BDBDBF',
            background: theme.centerChannelBg || '#f1f1f1',
            color: theme.centerChannelColor || 'black',
        },
        button: {
            fontFamily: 'GraphikArabic',
            fontSize: '15px',
            fontWeight: 'normal',
            borderRadius: '9px',
            color: theme.centerChannelColor || '#000',
        },
        buttonIcon: {
            paddingRight: '8px',
            fill: theme.buttonColor || '#fff',
        },
        validUntil: {
            marginTop: '10px',
        },
    };
});

export const VideoCallFromMobile: React.FC<Props> = ({link}) => {
    const theme = useSelector(getTheme); // ✅ جلب الـ theme من الـ Redux Store
    const style = getStyle(theme); // ✅ تمرير الـ theme عند استدعاء getStyle()

    return (
        <div>
            <div style={style.title1}>
                {'الاجتماع'}
                <div style={style.attachment}>
                    <div style={style.content}>
                        <div style={style.container}>
                            <h1 style={style.title}>
                                {'اجتماع'}
                            </h1>
                            <div>
                                <div style={style.body}>
                                    <div>
                                        <ExternalLink
                                            className='btn btn-lg btn-primary'
                                            style={style.button}
                                            href={link}
                                            location='post_videocall_mobile'
                                        >
                                            <i
                                                style={style.buttonIcon}
                                                dangerouslySetInnerHTML={{__html: svgs.VIDEO_CAMERA_3}}
                                            />
                                            <span> {'الانظمام إلى اجتماع'}</span>
                                        </ExternalLink>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};
